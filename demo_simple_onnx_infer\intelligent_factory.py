#!/usr/bin/env python3
import os
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union, Any
from dataclasses import dataclass
from collections import defaultdict

from ...manager.model import AudioModelManager


class AudioInferenceEngine:
    """Universal Audio Inference Engine"""
    @dataclass
    class Config:
        """Inference configuration parameters"""
        device: str = 'cuda'       # Device to use ('cuda', 'cpu') / 使用的设备
        window_size: float = 10.0  # Window size in seconds
        step_size: float = 1.0     # Step size in seconds
        threshold: float = 0.7     # Confidence threshold
        min_duration: float = 12.0 # Minimum duration in seconds
        batch_size: int = 32       # Batch size for inference
        use_onnx: bool = False     # Whether to use ONNX
        ensemble_method: str = 'min' # Ensemble method (min/max/mean/voting)
        class_name_mappings: Optional[Dict[str, str]] = None  # Class name mappings / 类别名称映射
    
    def __init__(self, model_root: Union[str, Path], config = None,
                 verbose: bool = False):
        """Initialize the inference engine

        Args:
            model_root: Path to model root directory containing config.yaml and checkpoint/
            config: Inference configuration, use default if not provided
            verbose: Whether to print progress information
        """
        self.model_root = Path(model_root)
        self.config = config
        self.verbose = verbose
        self._initialize_dependencies()
        self._initialize_model_manager()
        self._initialize_engine(verbose)
    
    def _initialize_dependencies(self):
        """Initialize required dependencies"""
        try:
            # Core dependencies
            import torch
            import torchaudio
            import numpy as np
            from rich.console import Console

            # Optional dependencies
            try:
                import onnxruntime as ort
            except ImportError:
                ort = None

            # Set global variables
            self.torch = torch
            self.torchaudio = torchaudio
            self.np = np
            self.console = Console() if self.verbose else None
            self.ort = ort

            # Import preprocessing adapter
            from ...shared_libs.intelligent_factory.utils.preprocessing_adapter import MultiDatasetPreprocessingAdapter
            self.preprocessing_adapter = MultiDatasetPreprocessingAdapter

        except ImportError as e:
            raise ImportError(f"Failed to import required dependencies: {e}")

    def _initialize_model_manager(self):
        """Initialize model manager"""
        try:
            self.device = self.config.device
            self.model_manager = AudioModelManager(self.model_root, self.device)

            # Get model information
            self.class_names = self.model_manager.class_names
            self.num_classes = self.model_manager.num_classes
            self.cfg = self.model_manager.cfg

        except Exception as e:
            raise RuntimeError(f"Failed to initialize model manager: {e}")
    
    def _initialize_engine(self, verbose: bool):
        """Initialize the inference engine"""
        # Initialize preprocessing
        self._initialize_preprocessing()

        if verbose:
            self._print_initialization_info()
    
    def _initialize_preprocessing(self):
        """Initialize preprocessing adapter"""
        self.preprocessor = self.preprocessing_adapter(self.cfg, self.device)
    
    def _print_initialization_info(self):
        """Print initialization information"""
        model_info = self.model_manager.get_model_info()
        self.console.print(f"[green]Initialized Audio Inference Engine:")
        self.console.print(f"  Model Root: {self.model_root}")
        self.console.print(f"  Device: {self.device}")
        self.console.print(f"  Number of Classes: {self.num_classes}")
        self.console.print(f"  Number of Checkpoints: {len(model_info['available_models'])}")
        self.console.print(f"  Inference Config:")
        for key, value in self.config.__dict__.items():
            self.console.print(f"    {key}: {value}")
    
    def infer(self, audio_path: Union[str, Path]) -> Dict[str, List[Tuple[float, float, float]]]:
        """Perform inference on an audio file
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Dictionary mapping class names to list of (start_time, end_time, confidence) tuples
        """
        audio_path = Path(audio_path)
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        # Load and preprocess audio
        wave, sr = self._load_audio(audio_path)
        
        # Load models
        models = self._load_models()
        
        # Perform inference with single model (base implementation)
        if len(models) == 1:
            results = self._single_model_predict(models[0], wave, sr)
        else:
            # For multiple models, use the first one in base implementation
            results = self._single_model_predict(models[0], wave, sr)
        
        return results
    
    def _load_audio(self, audio_path: Path) -> Tuple[Any, int]:
        """Load and preprocess audio file"""
        try:
            wave, sr = self.torchaudio.load(str(audio_path))
            if wave.shape[0] > 1:  # Convert to mono
                wave = wave[:1]
            if sr != self.cfg.dataset.data.sr:  # Resample if needed
                wave = self.torchaudio.functional.resample(wave, sr, self.cfg.dataset.data.sr)
            return wave, self.cfg.dataset.data.sr
        except Exception as e:
            raise RuntimeError(f"Failed to load audio file: {e}")
    
    def _load_models(self) -> List[Any]:
        """Load all models (PyTorch or ONNX)"""
        if self.config.use_onnx:
            return self._load_onnx_models()
        return self.model_manager.load_all_models()
    
    def _load_onnx_models(self) -> List[Any]:
        """Load or convert and load ONNX models"""
        onnx_models = []
        for model_info in self.model_manager.available_models:
            onnx_path = model_info.path.with_suffix('.onnx')
            if not onnx_path.exists():
                pytorch_model = self.model_manager.load_model(model_info)
                self._export_to_onnx(pytorch_model, onnx_path)
            onnx_models.append(self._load_single_onnx_model(onnx_path))
        return onnx_models
    
    def _load_single_onnx_model(self, onnx_path: Path) -> Any:
        """Load a single ONNX model"""
        if self.ort is None:
            raise ImportError("ONNX Runtime not available")
        return self.ort.InferenceSession(str(onnx_path))
    
    def _export_to_onnx(self, pytorch_model: Any, onnx_path: Path):
        """Export PyTorch model to ONNX format"""
        if self.ort is None:
            raise ImportError("ONNX Runtime not available")
        
        # Create dummy input
        info = self.preprocessor.get_preprocessing_info()
        dummy_input = self.torch.randn(3, 1, info['n_mels'], info['size_x']).to(self.device)
        
        # Export model
        self.torch.onnx.export(
            pytorch_model,
            dummy_input,
            str(onnx_path),
            export_params=True,
            opset_version=12,
            do_constant_folding=True,
            input_names=["input"],
            output_names=["output"]
        )
    
    def _single_model_predict(self, model: Any, wave: Any, sr: int) -> Dict[str, List[Tuple[float, float, float]]]:
        """Perform prediction with a single model"""
        segments = self._generate_sliding_windows(wave, sr)
        time_stamps = [i * self.config.step_size for i in range(len(segments))]
        predictions = []
        
        for i in range(0, len(segments), self.config.batch_size):
            batch = segments[i:i + self.config.batch_size]
            preds = self._predict_batch(model, batch, sr)
            predictions.extend(preds)
        
        return self._process_predictions(predictions, time_stamps)
    
    def _generate_sliding_windows(self, wave: Any, sr: int) -> List[Any]:
        """Generate sliding windows from audio"""
        window_samples = int(self.config.window_size * sr)
        step_samples = int(self.config.step_size * sr)
        total_samples = wave.shape[1]
        segments = []
        
        for start in range(0, total_samples, step_samples):
            end = start + window_samples
            if end > total_samples:
                pad_length = end - total_samples
                segment = self.torch.cat([wave[:, start:], wave[:, :pad_length]], dim=1)
            else:
                segment = wave[:, start:end]
            segments.append(segment)
        
        return segments
    
    def _predict_batch(self, model: Any, batch: List[Any], sr: int) -> List[Any]:
        """Predict on a batch of segments"""
        batch_tensors = self.torch.stack([
            self.preprocessor.preprocess_audio_segment(seg, sr, self.config.window_size)
            for seg in batch
        ]).to(self.device)
        
        if isinstance(model, self.torch.nn.Module):
            with self.torch.no_grad():
                outputs = model(batch_tensors)
                return self.torch.sigmoid(outputs).cpu().numpy()
        else:  # ONNX model
            outputs = model.run(None, {"input": batch_tensors.cpu().numpy()})
            return self.torch.sigmoid(self.torch.from_numpy(outputs[0])).numpy()
    
    def _process_predictions(self, predictions: List[Any], time_stamps: List[float]) -> Dict[str, List[Tuple[float, float, float]]]:
        """Process predictions from a single model"""
        bird_timestamps = defaultdict(list)
        
        for i, pred in enumerate(predictions):
            start_time = time_stamps[i]
            end_time = start_time + self.config.window_size
            for bird_idx, confidence in enumerate(pred):
                if confidence > self.config.threshold:
                    bird_name = self.class_names[bird_idx]
                    # Apply class name mapping
                    mapped_bird_name = self._apply_class_name_mapping(bird_name)
                    bird_timestamps[mapped_bird_name].append((start_time, end_time, confidence))
        
        return self._merge_timestamps(bird_timestamps)
    
    def _merge_timestamps(self, bird_timestamps: Dict[str, List[Tuple[float, float, float]]]) -> Dict[str, List[Tuple[float, float, float]]]:
        """Merge overlapping timestamps"""
        merged = {}
        for bird, timestamps in bird_timestamps.items():
            timestamps.sort(key=lambda x: x[0])
            merged_segments = []
            
            for start, end, conf in timestamps:
                if not merged_segments:
                    merged_segments.append([start, end, conf])
                else:
                    last_start, last_end, last_conf = merged_segments[-1]
                    if start <= last_end:
                        merged_segments[-1] = [
                            last_start,
                            max(end, last_end),
                            min(conf, last_conf)
                        ]
                    else:
                        merged_segments.append([start, end, conf])
            
            # Filter short segments
            valid_segments = [
                seg for seg in merged_segments
                if seg[1] - seg[0] >= self.config.min_duration
            ]
            
            if valid_segments:
                merged[bird] = valid_segments
        
        return merged

    def _apply_class_name_mapping(self, class_name: str) -> str:
        """Apply class name mapping if configured

        应用类别名称映射（如果已配置）

        Args:
            class_name: Original class name / 原始类别名称

        Returns:
            Mapped class name or original if no mapping exists / 映射后的类别名称，如果没有映射则返回原始名称
        """
        if self.config.class_name_mappings and class_name in self.config.class_name_mappings:
            return self.config.class_name_mappings[class_name]
        return class_name
