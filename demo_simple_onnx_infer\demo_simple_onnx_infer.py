import os
import sys
PROJECT_ROOT = os.path.abspath(os.path.join(
    os.path.dirname(__file__), ".."))
sys.path.insert(0, PROJECT_ROOT)
import torch
import torchaudio
import numpy as np
import onnxruntime as ort
from typing import List
from mytools.categories import changzhou_bird_names
from configs import BaseSoundConfig

# 动态导入项目模块
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, PROJECT_ROOT)

# 配置参数
cfg = BaseSoundConfig()
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
NUM_CLASSES = len(changzhou_bird_names)
THRESHOLD = 0.7  # 置信度阈值
ONNX_MODEL_PATH = "runs/bird2024exp1057_m-effb3/20250514-131241/checkpoint/best_model.onnx"

# Mel频谱转换层
spec_layer = torchaudio.transforms.MelSpectrogram(
    sample_rate=cfg.sr,
    hop_length=cfg.hop_length,
    n_fft=cfg.n_fft,
    n_mels=cfg.n_mels,
    f_min=cfg.fmin,
    f_max=cfg.fmax,
    power=cfg.power,
    mel_scale="slaney",
    center=True,
    pad_mode="reflect"
).to(DEVICE)

def load_onnx_model() -> ort.InferenceSession:
    """加载ONNX模型并配置执行提供者"""
    providers = ['CUDAExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
    session = ort.InferenceSession(ONNX_MODEL_PATH, providers=providers)
    print(f"Loaded ONNX model from {ONNX_MODEL_PATH}")
    return session

def generate_sliding_windows(wave: torch.Tensor, sr: int) -> List[torch.Tensor]:
    """生成固定10秒窗口的音频片段"""
    window_samples = 10 * sr  # 固定10秒窗口
    total_samples = wave.shape[1]
    segments = []

    for start in range(0, total_samples, window_samples):
        end = start + window_samples
        if end > total_samples:
            pad_len = end - total_samples
            segment = torch.cat([wave[:, start:], wave[:, :pad_len]], dim=1)
        else:
            segment = wave[:, start:end]
        segments.append(segment)
    return segments

def preprocess(wave: torch.Tensor) -> np.ndarray:
    """音频预处理并转换为ONNX需要的输入格式"""
    wave = wave.to(DEVICE)
    mel = spec_layer(wave)
    mel = torch.log(mel + 1e-6)
    mel = (mel - mel.min()) / (mel.abs().max() + 1e-8)

    # 调整维度为 [1, bs, n_mels, size_x]
    c = mel.shape[2]
    if c < cfg.size_x:
        mel = torch.nn.functional.pad(mel, (0, cfg.size_x - c), value=0)
    else:
        mel = mel[:, :, :cfg.size_x]

    # 确保形状为 [1, bs, n_mels, size_x]
    if len(mel.shape) == 3:
        mel = mel.unsqueeze(0)
    return mel.cpu().numpy()

def infer_onnx(session: ort.InferenceSession, audio_path: str):
    """使用ONNX模型进行推理，每个窗口只输出一个结果（最高置信度的类别）"""
    # 加载音频
    wave, sr = torchaudio.load(audio_path)
    if wave.shape[0] > 1:
        wave = wave[:1]  # 单声道
    if sr != cfg.sr:
        wave = torchaudio.functional.resample(wave, sr, cfg.sr)

    # 生成窗口
    segments = generate_sliding_windows(wave, cfg.sr)
    print(f"Processing {audio_path} with {len(segments)} windows")

    # 逐窗口推理
    for idx, seg in enumerate(segments):
        input_data = preprocess(seg)

        # ONNX推理
        input_name = session.get_inputs()[0].name
        outputs = session.run(None, {input_name: input_data})

        # 处理输出
        prob = 1 / (1 + np.exp(-outputs[0][0]))  # sigmoid
        max_idx = np.argmax(prob)
        max_prob = prob[max_idx]

        if max_prob > THRESHOLD:
            t0 = idx * 10  # 固定10秒步长
            t1 = t0 + 10
            print(
                f"Window: [{t0:.1f}-{t1:.1f}s], Class: {changzhou_bird_names[max_idx]}, Score: {max_prob:.2f}"
            )

if __name__ == "__main__":
    # 硬编码的测试文件路径
    TEST_AUDIO = "/home/<USER>/Works/transformers/data/archive/20250414定向增补/train/暗绿绣眼鸟/20220819_063900_12.572_17.576.wav"

    # 加载ONNX模型并推理
    ort_session = load_onnx_model()
    infer_onnx(ort_session, TEST_AUDIO)
