import onnxruntime as ort
session = ort.InferenceSession("E:/数据标注、识别、训练标准化平台/data/onnx/unencrypted/b2d70b6c63fc41b004784f281a32bd9f/img_beast.onnx")
classes = session.get_modelmeta().custom_metadata_map['names']
print(classes)
# print(len(['东方大苇莺', '丝光椋鸟', '丽星鹩鹛', '乌灰鸫', '乌鸫', '仙八色鸫', '八哥', '冕柳莺', '冕雀', '北灰鹟', '北红尾鸲', '华南冠纹柳莺', '华南斑胸钩嘴鹛', '叉尾太阳鸟', '双斑绿柳莺', '发冠卷尾', '喜鹊', '噪声', '噪鹃', '四声杜鹃', '夜鹭', '大山雀', '大拟啄木鸟', '大斑啄木鸟', '大杜鹃', '大鹰鹃', '家燕', '小䴙䴘', '小仙鹟', '小杜鹃', '小灰山椒鸟', '小鳞胸鹪鹛', '小鸦鹃', '小鹀', '山斑鸠', '山鹡鸰', '山麻雀', '强脚树莺', '戴胜', '扇尾沙锥', '斑姬啄木鸟', '斑尾鹃鸠', '斑文鸟', '斑鱼狗', '星头啄木鸟', '普通夜鹰', '普通秧鸡', '普通翠鸟', '暗灰鹃鵙', '暗绿绣眼鸟', '未知鸟类', '松鸦', '极北柳莺', '树鹨', '栗头鹟莺', '栗背短脚鹎', '栗颈凤鹛', '棕头鸦雀', '棕背伯劳', '棕脸鹟莺', '棕腹柳莺', '棕颈钩嘴鹛', '橙腹叶鹎', '池鹭', '淡眉雀鹛', '淡脚柳莺', '灰卷尾', '灰喉山椒鸟', '灰喜鹊', '灰头绿啄木鸟', '灰头鸦雀', '灰头鹀', '灰头麦鸡', '灰树鹊', '灰椋鸟', '灰背鸫', '灰胸竹鸡', '灰鹡鸰', '煤山雀', '环颈雉', '珠颈斑鸠', '画眉', '白喉林鹟', '白喉短翅鸫', '白头鹎', '白斑尾柳莺', '白眉姬鹟', '白眉山鹧鸪', '白眶鹟莺', '白胸苦恶鸟', '白腰文鸟', '白腰草鹬', '白腹凤鹛', '白腹鸫', '白额燕尾', '白鹇', '白鹡鸰', '白鹭', '矶鹬', '紫啸鸫', '红喉歌鸲', '红嘴相思鸟', '红嘴蓝鹊', '红头穗鹛', '红头长尾山雀', '红尾伯劳', '红尾歌鸲', '红翅凤头鹃', '红翅鵙鹛', '红胁蓝尾鸲', '红胸啄花鸟', '红角鸮', '纯色山鹪莺', '绿头鸭', '绿翅短脚鹎', '绿翅鸭', '苍鹭', '蛇雕', '褐柳莺', '褐灰雀', '赤腹鹰', '远东树莺', '金翅雀', '金腰燕', '银喉长尾山雀', '雉鸡', '青脚鹬', '领角鸮', '领雀嘴鹎', '领鸺鹠', '高山短翅蝗莺', '鸲姬鹟', '鹊鸲', '鹤鹬', '麻雀', '黄嘴栗啄木鸟', '黄嘴角鸮', '黄眉柳莺', '黄腰柳莺', '黄腹山鹪莺', '黄腹鹨', '黄臀鹎', '黄颊山雀', '黑冠鹃隼', '黑卷尾', '黑头蜡嘴雀', '黑尾蜡嘴雀', '黑枕黄鹂', '黑水鸡', '黑眉柳莺', '黑眉苇莺', '黑短脚鹎', '黑脸噪鹛', '黑领椋鸟', '黑颏凤鹛']))