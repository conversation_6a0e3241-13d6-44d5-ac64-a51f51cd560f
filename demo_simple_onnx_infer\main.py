import os
# Must be set before importing numpy/opencv to be effective
os.environ.setdefault("OMP_NUM_THREADS", "1")
os.environ.setdefault("MKL_NUM_THREADS", "1")
os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")
os.environ.setdefault("NUMEXPR_NUM_THREADS", "1")

import sys
import json
import time
import cv2
import numpy as np
import onnxruntime as ort
from Crypto.Cipher import AES
from types import SimpleNamespace
import wmi

from pathlib import Path

import gc

# Set OpenCV to single-threaded to avoid conflicts
cv2.setNumThreads(1)
sys.stdin.reconfigure(encoding='utf-8-sig')
sys.stdout.reconfigure(encoding='utf-8')
out_results = {"status": 0, "data": [], "message": "", "timestamp": 0, "progress": 0, "total": 0}
default_mel_w = 1000
default_mel_h = 400
def is_amd_gpu():
    try:
        c = wmi.WMI()
        for gpu in c.Win32_VideoController():
            if 'amd' in gpu.Name.lower() or 'radeon' in gpu.Name.lower():
                return True
        return False
    except:
        return False

class ImgOnnx:
    def __init__(self, onnx_model: str, input_images: list, conf_thres: float = 0.3,
                 conf_scrap: float = 0.7, iou_thres: float = 0.5, key_hex: str = None):
        self.onnx_model = onnx_model
        self.input_images = input_images
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.conf_scrap = conf_scrap
        self.key_hex = bytes.fromhex(key_hex) if key_hex else None
        self.classes = {}
        self.session = None
        self.threads = 8
        self.provider = None

    def decrypt_onnx(self):
        """解密 ONNX 模型"""
        with open(self.onnx_model, "rb") as f:
            nonce = f.read(16)
            tag = f.read(16)
            ciphertext = f.read()

        cipher = AES.new(self.key_hex, AES.MODE_EAX, nonce=nonce)
        return cipher.decrypt_and_verify(ciphertext, tag)

    def letterbox(self, img: np.ndarray, new_shape: tuple = (640, 640)):
        """保持宽高比进行填充"""
        shape = img.shape[:2]
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        new_unpad = (int(round(shape[1] * r)), int(round(shape[0] * r)))
        dw, dh = (new_shape[1] - new_unpad[0]) / 2, (new_shape[0] - new_unpad[1]) / 2

        img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))

        return img, (r, r), (top, left)

    def preprocess(self, img_path: str):
        """预处理图像"""
        # 使用 pathlib 处理路径问题
        path_obj = Path(img_path).resolve()

        # 优先使用 cv2.imread（对中文路径支持最好）
        img = cv2.imread(str(path_obj), cv2.IMREAD_COLOR)

        # 如果失败，使用 pathlib 的字节读取方法
        if img is None:
            try:
                img_bytes = path_obj.read_bytes()
                img = cv2.imdecode(np.frombuffer(img_bytes, dtype=np.uint8), cv2.IMREAD_COLOR)
            except:
                pass

        if img is None:
            raise ValueError(f"无法读取图像: {img_path}")

        img_info = {}
        img_info["img_height"], img_info["img_width"] = img.shape[:2]
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        img, ratio, pad = self.letterbox(img, (self.input_width, self.input_height))
        # Avoid extra copy by using astype directly
        img_data = (img.astype(np.float32) / 255.0).transpose(2, 0, 1)
        img_data = np.expand_dims(img_data, axis=0)
        return img_data, img_info, ratio, pad

    def postprocess(self, output, img_info, ratio, pad):
        """后处理模型输出"""
        outputs = np.transpose(np.squeeze(output[0]))
        rows = outputs.shape[0]

        boxes, scores, class_ids = [], [], []
        outputs[:, 0] -= pad[1]
        outputs[:, 1] -= pad[0]

        for i in range(rows):
            class_scores = outputs[i][4:]
            max_score = np.amax(class_scores)

            if max_score >= self.conf_thres:
                class_id = np.argmax(class_scores)
                x, y, w, h = outputs[i][0], outputs[i][1], outputs[i][2], outputs[i][3]
                left = max(0, int((x - w / 2) / ratio[0]))
                top = max(0, int((y - h / 2) / ratio[0]))
                width = min(img_info["img_width"] - left, int(w / ratio[0]))
                height = min(img_info["img_height"] - top, int(h / ratio[0]))

                class_ids.append(class_id)
                scores.append(max_score)
                boxes.append([left, top, left + width, top + height])

        result = []
        if boxes:
            indices = cv2.dnn.NMSBoxes(boxes, scores, self.conf_thres, self.iou_thres)
            try:
                indices = np.array(indices).reshape(-1).tolist()
            except Exception:
                indices = []
            for i in indices:
                box, score, class_id = boxes[i], round(float(scores[i]), 2), int(class_ids[i])
                result.append({"box": box, "score": score, "classId": class_id, "className": self.classes[class_id]})

        return result

    def process_image(self, img, model_input):
        """处理单张图片"""
        img_data, img_info, ratio, pad = self.preprocess(img)
        outputs = self.session.run(None, {model_input.name: img_data})
        result = self.postprocess(outputs, img_info, ratio, pad)  # 先计算结果
        # 添加内存清理
        del img, img_data, outputs  # 删除临时变量
        gc.collect()  # 强制垃圾回收
        return result

    def _select_providers(self):
        avail = ort.get_available_providers()
        if self.provider == "dml" and ort.get_device() == "CPU-DML" and not is_amd_gpu() and "DmlExecutionProvider" in avail:
            return ["DmlExecutionProvider", "CPUExecutionProvider"]
        return ["CPUExecutionProvider"]

    def _get_ort_session_options(self):
        so = ort.SessionOptions()
        # Safe default single-thread; allow override via self.threads (clamped)
        threads = int(getattr(self, "threads", 1) or 1)
        try:
            max_thr = max(1, min(4, os.cpu_count() or 4))
        except Exception:
            max_thr = 4
        threads = max(1, min(max_thr, threads))
        so.intra_op_num_threads = threads
        so.inter_op_num_threads = 1  # keep 1 for stability
        so.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        # Graph optimizations can help a lot and are generally safe on CPU
        so.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        # Memory pattern: keep disabled by default; allow opt-in for CPU-only
        mem_pattern = bool(getattr(self, "mem_pattern", False))
        so.enable_mem_pattern = mem_pattern
        so.enable_cpu_mem_arena = False
        return so

    def _supports_batching(self, model_input):
        try:
            bdim = model_input.shape[0]
            # Dynamic or >1 static batch is considered batchable
            if bdim is None:
                return True
            if isinstance(bdim, int) and bdim > 1:
                return True
        except Exception:
            pass
        return False

    def process(self):
        """处理图像并返回结果"""
        so = self._get_ort_session_options()

        providers = self._select_providers()
        self.session = ort.InferenceSession(self.decrypt_onnx(), sess_options=so, providers=providers)

        self.classes = eval(self.session.get_modelmeta().custom_metadata_map['names'])
        print(self.session.get_modelmeta().custom_metadata_map['names'])
        model_input = self.session.get_inputs()[0]
        self.input_width, self.input_height = model_input.shape[2], model_input.shape[3]

        results = []
        batch_size = max(1, int(getattr(self, "batch_size", 1)))
        use_batch = batch_size > 1 and self._supports_batching(model_input)

        if use_batch:
            batch_inputs, batch_contexts = [], []
            for img in self.input_images:
                img_info = img.copy()
                if img["status"] == 0:
                    try:
                        img_data, prep_info, ratio, pad = self.preprocess(img["targetFile"])
                        batch_inputs.append(img_data)
                        batch_contexts.append((img_info, prep_info, ratio, pad))
                    except Exception as e:
                        img_info["status"] = 0
                        img_info["message"] = f"处理失败: {e}"
                        results.append(img_info)
                        out_results["progress"] += 1
                        out_results['data'] = [img_info]
                        should_print = (out_results["progress"] % getattr(self, "progress_every", 1) == 0) or (out_results["progress"] >= out_results.get("total", 0))
                        if should_print:
                            try:
                                print(json.dumps(out_results, ensure_ascii=False), flush=True)
                            except UnicodeEncodeError:
                                print(json.dumps(out_results, ensure_ascii=True), flush=True)
                            print("----END----", flush=True)
                        continue
                else:
                    # If not pending, just pass through
                    results.append(img_info)
                    out_results["progress"] += 1
                    out_results['data'] = [img_info]
                    should_print = (out_results["progress"] % getattr(self, "progress_every", 1) == 0) or (out_results["progress"] >= out_results.get("total", 0))
                    if should_print:
                        try:
                            print(json.dumps(out_results, ensure_ascii=False), flush=True)
                        except UnicodeEncodeError:
                            print(json.dumps(out_results, ensure_ascii=True), flush=True)
                        print("----END----", flush=True)
                    continue

                # Run a batch when full
                if len(batch_inputs) >= batch_size:
                    try:
                        input_name = model_input.name
                        batch_array = np.concatenate(batch_inputs, axis=0)
                        outputs = self.session.run(None, {input_name: batch_array})
                        out0 = outputs[0]
                        for bi, (img_info, prep_info, ratio, pad) in enumerate(batch_contexts):
                            try:
                                sample = out0[bi]
                                sample_out = [np.expand_dims(sample, axis=0)]
                                data = self.postprocess(sample_out, prep_info, ratio, pad)
                                img_info["status"] = 2 if not data  else 1
                                img_info["bboxes"] = data
                            except Exception as e:
                                img_info["status"] = 0
                                img_info["message"] = f"处理失败: {e}"
                            results.append(img_info)
                            out_results["progress"] += 1
                            out_results['data'] = [img_info]
                            should_print = (out_results["progress"] % getattr(self, "progress_every", 1) == 0) or (out_results["progress"] >= out_results.get("total", 0))
                            if should_print:
                                try:
                                    print(json.dumps(out_results, ensure_ascii=False), flush=True)
                                except UnicodeEncodeError:
                                    print(json.dumps(out_results, ensure_ascii=True), flush=True)
                                print("----END----", flush=True)
                    finally:
                        # Clear batch buffers
                        del batch_inputs[:]
                        del batch_contexts[:]
                        gc.collect()

            # Flush remaining
            if batch_inputs:
                try:
                    input_name = model_input.name
                    batch_array = np.concatenate(batch_inputs, axis=0)
                    outputs = self.session.run(None, {input_name: batch_array})
                    out0 = outputs[0]
                    for bi, (img_info, prep_info, ratio, pad) in enumerate(batch_contexts):
                        try:
                            sample = out0[bi]
                            sample_out = [np.expand_dims(sample, axis=0)]
                            data = self.postprocess(sample_out, prep_info, ratio, pad)
                            img_info["status"] = 2 if not data else 3 if max(item["score"] for item in data) < self.conf_scrap else 1
                            img_info["bboxes"] = data
                        except Exception as e:
                            img_info["status"] = 0
                            img_info["message"] = f"处理失败: {e}"
                        results.append(img_info)
                        out_results["progress"] += 1
                        out_results['data'] = [img_info]
                        should_print = (out_results["progress"] % getattr(self, "progress_every", 1) == 0) or (out_results["progress"] >= out_results.get("total", 0))
                        if should_print:
                            try:
                                print(json.dumps(out_results, ensure_ascii=False), flush=True)
                            except UnicodeEncodeError:
                                print(json.dumps(out_results, ensure_ascii=True), flush=True)
                            print("----END----", flush=True)
                finally:
                    del batch_inputs[:]
                    del batch_contexts[:]
                    gc.collect()
        else:
            # Fallback: original per-image processing
            for img in self.input_images:
                img_info = img.copy()
                if(img["status"] == 0):
                    try:
                        data = self.process_image(img["targetFile"], model_input)
                        img_info["status"] = 2 if not data else 3 if max(item["score"] for item in data) < self.conf_scrap else 1
                        img_info["bboxes"] = data
                        results.append(img_info)
                    except Exception as e:
                        img_info["status"] = 0
                        img_info["message"] = f"处理失败: {e}"
                        out_results["status"] = 0
                        out_results["message"] = f"处理失败: {e}"
                        results.append(img_info)

                out_results["progress"] += 1
                out_results['data'] = [img_info]
                # Throttle progress printing to reduce stdout backpressure
                should_print = (out_results["progress"] % getattr(self, "progress_every", 1) == 0) or (out_results["progress"] >= out_results.get("total", 0))
                if should_print:
                    try:
                        # 优先使用 ensure_ascii=False 保持可读性
                        print(json.dumps(out_results, ensure_ascii=False), flush=True)
                    except UnicodeEncodeError:
                        # 出现编码问题时回退到 ensure_ascii=True
                        print(json.dumps(out_results, ensure_ascii=True), flush=True)
                    print("----END----", flush=True)

        # Explicit cleanup
        session = self.session
        self.session = None
        del session
        gc.collect()

        return results

class AudioProcessor:
    def __init__(self, audio_paths: list, output_path: str,mel_w: int = default_mel_w,mel_h: int = default_mel_h):
        # Lazy imports to avoid loading heavy libs on image-only runs
        import matplotlib
        matplotlib.use("Agg")
        import matplotlib.pyplot as plt
        import librosa
        from pydub import AudioSegment

        self.plt = plt
        self.librosa = librosa
        self.AudioSegment = AudioSegment

        self.audio_paths = audio_paths
        self.output_path = output_path
        self.mel_w = mel_w // 100 #语谱图的宽度
        self.mel_h = mel_h // 100#语谱图的高度

    def load_audio_segment(self, file_path):
        """加载音频文件"""
        ext = Path(file_path).suffix.lower()
        if ext == '.mp3':
            return self.AudioSegment.from_mp3(file_path)
        elif ext == '.wav':
            return self.AudioSegment.from_wav(file_path)
        else:
            raise ValueError("Unsupported file format. Only .mp3 and .wav are supported.")

    def generate_mel_spectrogram(self, audio_data, sample_rate, output_path):
        """生成 Mel 频谱图"""
        S = self.librosa.feature.melspectrogram(y=audio_data, sr=sample_rate, n_mels=128, fmax=8000)
        S_dB = self.librosa.power_to_db(S, ref=np.max)

        fig = self.plt.figure(figsize=(self.mel_w, self.mel_h))
        ax = fig.add_subplot(111)
        self.librosa.display.specshow(S_dB, sr=sample_rate, fmax=8000, ax=ax)
        self.plt.tight_layout(pad=0)
        self.plt.savefig(output_path)
        self.plt.close(fig)
        return "SUCCESS"

    def process_segment(self, file_path):
        """处理音频片段并生成频谱图"""
        file_name = Path(file_path).stem
        spectrogram_path = (Path(self.output_path) / file_name).with_suffix('.jpg')

        audio_data, sample_rate = self.librosa.load(file_path, sr=None)
        status = self.generate_mel_spectrogram(audio_data, sample_rate, spectrogram_path)

        if status == "SUCCESS":
            return {
                "sourceFile": str(file_path),           # 确保是字符串
                "sliceFile": str(file_path),            # 确保是字符串
                "targetFile": str(spectrogram_path),    # 转换为字符串
                "frameNumber": 0,
            }
        return None

    def process(self):
        """处理所有音频文件"""
        result_list = []
        for path in self.audio_paths:
            data = self.process_segment(path)
            result_list.append(data)
        return result_list

class AudioOnnx:
    def __init__(self, onnx_model: str, input_audios: list, key_hex: str,
                conf_thres: float = 0.3, conf_scrap: float = 0.7,
                mel_wh: str = f"{default_mel_w}/{default_mel_h}",
                labels: list = None,):
        # Lazy imports to avoid heavy deps on image-only runs
        import torch
        import torchaudio
        import librosa



        self.torch = torch
        self.torchaudio = torchaudio
        self.librosa = librosa

        self.onnx_model = onnx_model
        self.input_audios = input_audios
        self.key_hex = bytes.fromhex(key_hex)
        self.session = None
        self.conf_scrap = conf_scrap
        self.conf_thres = conf_thres
        self.mel_wh = mel_wh
        self.threads = 8
        self.provider = None

        self.labels = set([str(x).strip()for x in labels if str(x).strip()]) if labels else None #传入的地区名录

        self.cfg = SimpleNamespace(
            sr=32000,
            fmin=40,
            fmax=15000,
            hop_length=500,
            n_mels=128,
            n_fft=1024,
            power=2,
            size_x=512,
            test_hop_length=500
        )
        self.spec_layer = self.torchaudio.transforms.MelSpectrogram(
                  sample_rate=self.cfg.sr,
                  hop_length=self.cfg.hop_length,
                  n_fft=self.cfg.n_fft,
                  n_mels=self.cfg.n_mels,
                  f_min=self.cfg.fmin,
                  f_max=self.cfg.fmax,
                  power=self.cfg.power,
                  mel_scale="slaney",
                  center=True,
                  pad_mode="reflect"
              )
        self.label = ['东方大苇莺', '丝光椋鸟', '丽星鹩鹛', '乌灰鸫', '乌鸫', '仙八色鸫', '八哥', '冕柳莺', '冕雀', '北灰鹟', '北红尾鸲', '华南冠纹柳莺', '华南斑胸钩嘴鹛', '叉尾太阳鸟', '双斑绿柳莺', '发冠卷尾', '喜鹊', '噪声', '噪鹃', '四声杜鹃', '夜鹭', '大山雀', '大拟啄木鸟', '大斑啄木鸟', '大杜鹃', '大鹰鹃', '家燕', '小䴙䴘', '小仙鹟', '小杜鹃', '小灰山椒鸟', '小鳞胸鹪鹛', '小鸦鹃', '小鹀', '山斑鸠', '山鹡鸰', '山麻雀', '强脚树莺', '戴胜', '扇尾沙锥', '斑姬啄木鸟', '斑尾鹃鸠', '斑文鸟', '斑鱼狗', '星头啄木鸟', '普通夜鹰', '普通秧鸡', '普通翠鸟', '暗灰鹃鵙', '暗绿绣眼鸟', '未知鸟类', '松鸦', '极北柳莺', '树鹨', '栗头鹟莺', '栗背短脚鹎', '栗颈凤鹛', '棕头鸦雀', '棕背伯劳', '棕脸鹟莺', '棕腹柳莺', '棕颈钩嘴鹛', '橙腹叶鹎', '池鹭', '淡眉雀鹛', '淡脚柳莺', '灰卷尾', '灰喉山椒鸟', '灰喜鹊', '灰头绿啄木鸟', '灰头鸦雀', '灰头鹀', '灰头麦鸡', '灰树鹊', '灰椋鸟', '灰背鸫', '灰胸竹鸡', '灰鹡鸰', '煤山雀', '环颈雉', '珠颈斑鸠', '画眉', '白喉林鹟', '白喉短翅鸫', '白头鹎', '白斑尾柳莺', '白眉姬鹟', '白眉山鹧鸪', '白眶鹟莺', '白胸苦恶鸟', '白腰文鸟', '白腰草鹬', '白腹凤鹛', '白腹鸫', '白额燕尾', '白鹇', '白鹡鸰', '白鹭', '矶鹬', '紫啸鸫', '红喉歌鸲', '红嘴相思鸟', '红嘴蓝鹊', '红头穗鹛', '红头长尾山雀', '红尾伯劳', '红尾歌鸲', '红翅凤头鹃', '红翅鵙鹛', '红胁蓝尾鸲', '红胸啄花鸟', '红角鸮', '纯色山鹪莺', '绿头鸭', '绿翅短脚鹎', '绿翅鸭', '苍鹭', '蛇雕', '褐柳莺', '褐灰雀', '赤腹鹰', '远东树莺', '金翅雀', '金腰燕', '银喉长尾山雀', '雉鸡', '青脚鹬', '领角鸮', '领雀嘴鹎', '领鸺鹠', '高山短翅蝗莺', '鸲姬鹟', '鹊鸲', '鹤鹬', '麻雀', '黄嘴栗啄木鸟', '黄嘴角鸮', '黄眉柳莺', '黄腰柳莺', '黄腹山鹪莺', '黄腹鹨', '黄臀鹎', '黄颊山雀', '黑冠鹃隼', '黑卷尾', '黑头蜡嘴雀', '黑尾蜡嘴雀', '黑枕黄鹂', '黑水鸡', '黑眉柳莺', '黑眉苇莺', '黑短脚鹎', '黑脸噪鹛', '黑领椋鸟', '黑颏凤鹛']

    def decrypt_onnx(self):
        """解密 ONNX 模型"""
        with open(self.onnx_model, "rb") as f:
            nonce = f.read(16)
            tag = f.read(16)
            ciphertext = f.read()

        cipher = AES.new(self.key_hex, AES.MODE_EAX, nonce=nonce)
        return cipher.decrypt_and_verify(ciphertext, tag)
    def generate_sliding_windows(self,wave, sr):
      """生成固定10秒窗口的音频片段"""
      window_samples = 10 * sr  # 固定10秒窗口
      total_samples = wave.shape[1]
      segments = []

      for start in range(0, total_samples, window_samples):
          end = start + window_samples
          if end > total_samples:
              pad_len = end - total_samples
              segment = self.torch.cat([wave[:, start:], wave[:, :pad_len]], dim=1)
          else:
              segment = wave[:, start:end]
          segments.append(segment)
      return segments
    def preprocess(self,wave):
      """音频预处理并转换为ONNX需要的输入格式"""
      mel = self.spec_layer(wave)
      mel = self.torch.log(mel + 1e-6)
      mel = (mel - mel.min()) / (mel.abs().max() + 1e-8)

      # 调整维度为 [1, bs, n_mels, size_x]
      c = mel.shape[2]
      if c < self.cfg.size_x:
          mel = self.torch.nn.functional.pad(mel, (0, self.cfg.size_x - c), value=0)
      else:
          mel = mel[:, :, :self.cfg.size_x]

      # 确保形状为 [1, bs, n_mels, size_x]
      if len(mel.shape) == 3:
          mel = mel.unsqueeze(0)
      return mel.cpu().numpy()
    def merge_consecutive_results(self, results):
        """合并连续相同className的结果，score取最小值"""
        if not results:
            return results

        # 按时间顺序排序
        results.sort(key=lambda x: x['box'][0])

        merged = []
        current_group = [results[0]]

        for i in range(1, len(results)):
            current_result = results[i]
            previous_result = results[i-1]

            # 如果当前结果和前一个结果的className相同，且时间连续（假设步长为10秒）
            if (current_result['className'] == previous_result['className'] and
                current_result['box'][0] == previous_result['box'][1]):  # 时间连续
                current_group.append(current_result)
            else:
                # 结束当前组，开始新组
                merged.append(self.merge_group(current_group))
                current_group = [current_result]

        # 处理最后一组
        if current_group:
            merged.append(self.merge_group(current_group))

        return merged

    def merge_group(self, group):
        """合并一组结果"""
        if len(group) == 1:
            return group[0]

        # 获取时间范围
        start_time = group[0]['box'][0]
        end_time = group[-1]['box'][1]
        duration = group[0]['box'][2]
        mel_wh = group[0]['box'][3]

        # className和classId保持不变
        class_name = group[0]['className']
        class_id = group[0]['classId']

        # score取最小值
        min_score = min(item['score'] for item in group)

        return {
            "box": [start_time, end_time, duration, mel_wh],
            "score": min_score,
            "className": class_name,
            "classId": class_id
        }


    def predict_audios(self, path: str):
        results = []
        """使用ONNX模型进行推理，每个窗口只输出一个结果（最高置信度的类别）"""
        # 加载音频
        wave, sr = self.torchaudio.load(path)
        duration = wave.shape[1] / sr  # 计算音频总时长（秒）
        if wave.shape[0] > 1:
            wave = wave[:1]  # 单声道
        if sr != self.cfg.sr:
            wave = self.torchaudio.functional.resample(wave, sr, self.cfg.sr)

        # 生成窗口
        segments = self.generate_sliding_windows(wave, self.cfg.sr)
        # 逐窗口推理
        for idx, seg in enumerate(segments):
            input_data = self.preprocess(seg)

            # ONNX推理
            input_name = self.session.get_inputs()[0].name
            outputs = self.session.run(None, {input_name: input_data})

            # 处理输出
            prob = 1 / (1 + np.exp(-outputs[0][0]))  # sigmoid
            max_idx = np.argmax(prob)
            max_prob = prob[max_idx]

            if max_prob > self.conf_scrap:
                t0 = idx * 10  # 固定10秒步长
                t1 = t0 + 10
                results.append({
                    "box": [t0,t1,duration,self.mel_wh],
                    "score": round(float(max_prob), 2),
                    "className": self.label[max_idx],
                    "classId": int(max_idx)
                })
        # 合并连续相同className的结果
        merged_results = self.merge_consecutive_results(results)
        return sorted(merged_results, key=lambda x: x['score'], reverse=True)

    def _select_providers(self):
        avail = ort.get_available_providers()
        if self.provider == "dml" and ort.get_device() == "CPU-DML" and not is_amd_gpu() and "DmlExecutionProvider" in avail:
            return ["DmlExecutionProvider", "CPUExecutionProvider"]
        return ["CPUExecutionProvider"]
    def _get_ort_session_options(self):
        so = ort.SessionOptions()
        # Safe default single-thread; allow override via self.threads (clamped)
        threads = int(getattr(self, "threads", 1) or 1)
        try:
            max_thr = max(1, min(4, os.cpu_count() or 4))
        except Exception:
            max_thr = 4
        threads = max(1, min(max_thr, threads))
        so.intra_op_num_threads = threads
        so.inter_op_num_threads = 1  # keep 1 for stability
        so.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        # Graph optimizations can help a lot and are generally safe on CPU
        so.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        # Memory pattern: keep disabled by default; allow opt-in for CPU-only
        mem_pattern = bool(getattr(self, "mem_pattern", False))
        so.enable_mem_pattern = mem_pattern
        so.enable_cpu_mem_arena = False
        return so

    def process(self):
        """处理音频数据"""
        so = self._get_ort_session_options()
        # so.intra_op_num_threads = 1
        # so.inter_op_num_threads = 1
        # so.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        # so.enable_mem_pattern = False
        # so.enable_cpu_mem_arena = False

        providers = self._select_providers()
        session = ort.InferenceSession(self.decrypt_onnx(), sess_options=so, providers=providers)
        self.session = session
        results = []

        for audio in self.input_audios:
            try:
                data = self.predict_audios(audio["sliceFile"])
                audio["status"] = 2 if not data  else 1
                audio["bboxes"] = data
            except Exception as e:
                audio["status"] = 0
                audio["message"] = f"处理失败: {e}"
            results.append(audio)

            out_results["progress"] += 1
            out_results['data'] = [audio]
            # Throttle progress printing to reduce stdout backpressure
            should_print = (out_results["progress"] % getattr(self, "progress_every", 1) == 0) or (out_results["progress"] >= out_results.get("total", 0))
            if should_print:
                try:
                    print(json.dumps(out_results, ensure_ascii=False), flush=True)
                except:
                    print(json.dumps(out_results, ensure_ascii=True), flush=True)
                print("----END----", flush=True)

        # Explicit cleanup
        session = self.session
        self.session = None
        del session
        gc.collect()

        return results

if __name__ == "__main__":

    # stdin_data = sys.stdin.read()

    # stdin_data = '{"type":"img_onnx","input_data":{"modelPath":"E:/数据标注、识别、训练标准化平台/preprocessing/resources/img_bird_encrypted.onnx","data":[{"id":141,"taskId":10,"type":1,"sourceFile":"E:/demo/fad2156d-9c88-4229-9d70-c6ccd3864cef/八哥.jpg","sliceFile":"E:/demo/fad2156d-9c88-4229-9d70-c6ccd3864cef/八哥.jpg","targetFile":"E:/assets/image/鸟类/img/bage/八哥.jpg","fileName":"八哥.jpg","status":0,"frameNumber":1,"bboxes":[],"device":"[]","createTime":"2025-04-30 17:53:36","updateTime":"2025-04-30 17:53:36"}],"key":"b2d70b6c63fc41b004784f281a32bd9f"}}'
    # stdin_data = '{"type":"audio_onnx","input_data":{"modelPath":"E:/数据标注、识别、训练标准化平台/preprocessing/resources/audio_bird_encrypted.onnx","data":[{"id":23058,"taskId":57,"type":3,"sourceFile":"E:/demo/10b59327-f0cf-4762-a8db-8815219b324a/104.wav","sliceFile":"E:/demo/10b59327-f0cf-4762-a8db-8815219b324a/104.wav","targetFile":"E:/demo/10b59327-f0cf-4762-a8db-8815219b324a/104.jpg","fileName":"104.wav","status":0,"frameNumber":0,"bboxes":[],"device":"[]","createTime":"2025-07-03 11:46:43","updateTime":"2025-07-03 11:46:43"}],"key":"8e27ed16f093c50260fe9fbcd0b02acf"}}'


    stdin_data = '{"type":"audio_onnx","input_data":{"modelPath":"E:/数据标注、识别、训练标准化平台/preprocessing/resources/audio_bird_encrypted.onnx","data":[{"id":25326,"taskId":79,"type":3,"sourceFile":"E:/assets/音频/demo/八哥-7.wav","sliceFile":"E:/assets/音频/demo/八哥-7.wav","targetFile":"E:/demo/dcacc33d-40a7-42f2-9ebb-5a3730f0f2b4/$RJZ75H1.jpg","fileName":"$RJZ75H1.mp3","status":0,"frameNumber":0,"bboxes":[],"device":"[]","createTime":"2025-08-05 16:49:32","updateTime":"2025-08-05 16:49:32"}],"key":"b2d70b6c63fc41b004784f281a32bd9f"}}'

    # stdin_data = '{"type":"audio_processor","input_data":{"data":["E:/demo/ac537e33-3890-44ee-8931-717ae98db7ec/104.wav","E:/demo/ac537e33-3890-44ee-8931-717ae98db7ec/八哥-7.wav"],"outputPath":"E:/demo/ac537e33-3890-44ee-8931-717ae98db7ec"}}'

    out_results["status"] = 2


    try:
        parsed_data = json.loads(stdin_data)
        out_results["timestamp"] = time.time()
        input_data = parsed_data["input_data"]
        out_results["total"] = len(input_data['data'])
        out_results["progress"] = 0

        if parsed_data["type"] == "img_onnx":
            processor = ImgOnnx(
                input_data["modelPath"],
                input_data["data"],
                conf_thres=0.3,
                conf_scrap=0.7,
                iou_thres=0.5,
                key_hex=input_data["key"]
            )
            # Optional: support provider and progress throttling without breaking interface
            if "provider" in input_data:
                processor.provider = input_data["provider"]
            processor.progress_every = max(1, int(input_data.get("progress_every", 1)))

            out_results["data"] = processor.process()
        elif parsed_data["type"] == "audio_onnx":
            processor = AudioOnnx(
                onnx_model=input_data["modelPath"],
                input_audios=input_data["data"],
                key_hex=input_data["key"],
                mel_wh=input_data.get("mel_wh", f"{default_mel_w}/{default_mel_h}"),
                labels=input_data.get("labels", None)  # 如果有labels参数的话
            )
            if "provider" in input_data:
                processor.provider = input_data["provider"]
            processor.progress_every = max(1, int(input_data.get("progress_every", 1)))

            out_results["data"] = processor.process()
        elif parsed_data["type"] == "audio_processor":
            processor = AudioProcessor(
                input_data["data"],
                input_data["outputPath"],
                input_data.get("mel_w", default_mel_w),   # 如果没有 "mel_w"，使用默认值 1000
                input_data.get("mel_h", default_mel_h)     # 如果没有 "mel_h"，使用默认值 400
            )
            out_results["data"] = processor.process()

        out_results["status"] = 1
        out_results["timestamp"] = int((time.time() - out_results["timestamp"]) * 1000)
    except Exception as e:
        out_results["status"] = 0
        out_results["message"] = f"错误: {e}"

    try:
        # 优先使用 ensure_ascii=False 保持可读性
        print(json.dumps(out_results, ensure_ascii=False), flush=True)
    except UnicodeEncodeError:
        # 出现编码问题时回退到 ensure_ascii=True
        print(json.dumps(out_results, ensure_ascii=True), flush=True)
    print("----END----", flush=True)
    # print(json.dumps(out_results, ensure_ascii=False), flush=True)